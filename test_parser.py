#!/usr/bin/env python3
"""
Test script to verify conversation JSON parsing without loading into Graphiti.
This helps debug the JSON structure and message extraction.
"""

import json
from datetime import datetime, timezone
from typing import Dict, List, Any


def parse_conversations(json_file_path: str) -> List[Dict[str, Any]]:
    """Parse the conversations JSON file."""
    with open(json_file_path, 'r', encoding='utf-8') as f:
        conversations = json.load(f)
    
    print(f"Loaded {len(conversations)} conversations from {json_file_path}")
    return conversations


def extract_conversation_messages(conversation: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract messages from a conversation in chronological order."""
    mapping = conversation.get('mapping', {})
    messages = []
    
    # Find all messages with actual content
    for node_id, node_data in mapping.items():
        message = node_data.get('message')
        if not message:
            continue
            
        # Skip system messages and empty messages
        author = message.get('author', {})
        role = author.get('role', '')
        content = message.get('content', {})
        parts = content.get('parts', [])
        
        if role == 'system' or not parts or not parts[0]:
            continue
        
        # Extract message text
        message_text = parts[0] if parts else ""

        # Handle case where message_text might be a dict (e.g., tool responses)
        if isinstance(message_text, dict):
            # Skip tool messages or convert to string representation
            continue

        if not message_text or not str(message_text).strip():
            continue
        
        create_time = message.get('create_time')
        if create_time:
            timestamp = datetime.fromtimestamp(create_time, tz=timezone.utc)
        else:
            # Use conversation create_time as fallback
            timestamp = datetime.fromtimestamp(
                conversation.get('create_time', 0), 
                tz=timezone.utc
            )
        
        messages.append({
            'id': message.get('id', node_id),
            'role': role,
            'content': message_text[:100] + "..." if len(message_text) > 100 else message_text,
            'timestamp': timestamp,
            'parent': node_data.get('parent'),
            'children': node_data.get('children', [])
        })
    
    # Sort messages by timestamp
    messages.sort(key=lambda x: x['timestamp'])
    return messages


def test_conversation_parsing(json_file_path: str):
    """Test the conversation parsing without loading into Graphiti."""
    print("=" * 60)
    print("TESTING CONVERSATION PARSING")
    print("=" * 60)
    
    try:
        # Parse conversations
        conversations = parse_conversations(json_file_path)
        
        # Analyze each conversation
        for i, conversation in enumerate(conversations):
            title = conversation.get('title', 'Untitled Conversation')
            conversation_id = conversation.get('id', conversation.get('conversation_id', 'unknown'))
            create_time = conversation.get('create_time', 0)
            update_time = conversation.get('update_time', 0)
            
            print(f"\nConversation {i+1}: {title}")
            print(f"  ID: {conversation_id}")
            print(f"  Created: {datetime.fromtimestamp(create_time, tz=timezone.utc) if create_time else 'Unknown'}")
            print(f"  Updated: {datetime.fromtimestamp(update_time, tz=timezone.utc) if update_time else 'Unknown'}")
            
            # Extract messages
            messages = extract_conversation_messages(conversation)
            print(f"  Messages: {len(messages)}")
            
            # Show first few messages
            for j, message in enumerate(messages[:3]):  # Show first 3 messages
                print(f"    Message {j+1} ({message['role']}): {message['content']}")
            
            if len(messages) > 3:
                print(f"    ... and {len(messages) - 3} more messages")
        
        print(f"\n" + "=" * 60)
        print(f"SUMMARY: Found {len(conversations)} conversations")
        total_messages = sum(len(extract_conversation_messages(conv)) for conv in conversations)
        print(f"Total messages across all conversations: {total_messages}")
        print("=" * 60)
        
    except Exception as e:
        print(f"Error during parsing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Test with your conversations file
    CONVERSATIONS_FILE = 'conversations copy.json'
    
    import os
    if not os.path.exists(CONVERSATIONS_FILE):
        print(f"Error: File '{CONVERSATIONS_FILE}' not found!")
        print("Please make sure the file path is correct.")
    else:
        test_conversation_parsing(CONVERSATIONS_FILE)
