#!/usr/bin/env python3
"""
<PERSON>ript to load ChatGPT conversation data into Graphiti.

This script parses a JSON file containing ChatGPT conversations and loads each conversation
as episodes into a Graphiti knowledge graph. Each message in a conversation becomes an
episode, and the conversation structure is preserved through metadata.
"""

import asyncio
import json
import os
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

from dotenv import load_dotenv
from graphiti_core.graphiti import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.llm_client import OpenAIClient
from graphiti_core.embedder import OpenAIEmbedder

# Load environment variables
load_dotenv()


class ConversationLoader:
    """Loads ChatGPT conversations into Graphiti."""
    
    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str):
        """Initialize the conversation loader.
        
        Args:
            neo4j_uri: Neo4j database URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
        """
        self.graphiti = Graphiti(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            llm_client=OpenAIClient(),
            embedder=OpenAIEmbedder()
        )
    
    def parse_conversations(self, json_file_path: str) -> List[Dict[str, Any]]:
        """Parse the conversations JSON file.
        
        Args:
            json_file_path: Path to the conversations JSON file
            
        Returns:
            List of parsed conversation dictionaries
        """
        with open(json_file_path, 'r', encoding='utf-8') as f:
            conversations = json.load(f)
        
        print(f"Loaded {len(conversations)} conversations from {json_file_path}")
        return conversations
    
    def extract_conversation_messages(self, conversation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract messages from a conversation in chronological order.
        
        Args:
            conversation: Single conversation dictionary
            
        Returns:
            List of message dictionaries in chronological order
        """
        mapping = conversation.get('mapping', {})
        messages = []
        
        # Find all messages with actual content
        for node_id, node_data in mapping.items():
            message = node_data.get('message')
            if not message:
                continue
                
            # Skip system messages and empty messages
            author = message.get('author', {})
            role = author.get('role', '')
            content = message.get('content', {})
            parts = content.get('parts', [])
            
            if role == 'system' or not parts or not parts[0]:
                continue
            
            # Extract message text
            message_text = parts[0] if parts else ""

            # Handle case where message_text might be a dict (e.g., tool responses)
            if isinstance(message_text, dict):
                # Skip tool messages or convert to string representation
                continue

            if not message_text or not str(message_text).strip():
                continue
            
            create_time = message.get('create_time')
            if create_time:
                timestamp = datetime.fromtimestamp(create_time, tz=timezone.utc)
            else:
                # Use conversation create_time as fallback
                timestamp = datetime.fromtimestamp(
                    conversation.get('create_time', 0), 
                    tz=timezone.utc
                )
            
            messages.append({
                'id': message.get('id', node_id),
                'role': role,
                'content': message_text,
                'timestamp': timestamp,
                'parent': node_data.get('parent'),
                'children': node_data.get('children', [])
            })
        
        # Sort messages by timestamp
        messages.sort(key=lambda x: x['timestamp'])
        return messages
    
    async def load_conversation(self, conversation: Dict[str, Any], group_id: str = "conversations") -> None:
        """Load a single conversation into Graphiti.
        
        Args:
            conversation: Conversation dictionary
            group_id: Group ID for the conversation episodes
        """
        title = conversation.get('title', 'Untitled Conversation')
        conversation_id = conversation.get('id', conversation.get('conversation_id', 'unknown'))
        
        print(f"Loading conversation: {title}")
        
        # Extract messages from the conversation
        messages = self.extract_conversation_messages(conversation)
        
        if not messages:
            print(f"  No messages found in conversation: {title}")
            return
        
        print(f"  Found {len(messages)} messages")
        
        # Load each message as an episode
        for i, message in enumerate(messages):
            episode_name = f"{title} - Message {i+1}"
            
            # Format the message content with role information
            role = message['role']
            content = message['content']
            
            if role == 'user':
                episode_body = f"User: {content}"
            elif role == 'assistant':
                episode_body = f"Assistant: {content}"
            else:
                episode_body = f"{role.title()}: {content}"
            
            try:
                await self.graphiti.add_episode(
                    name=episode_name,
                    episode_body=episode_body,
                    source=EpisodeType.message,
                    source_description=f"ChatGPT conversation: {title}",
                    reference_time=message['timestamp'],
                    group_id=group_id
                )
                print(f"    Added message {i+1}/{len(messages)}: {role}")
                
            except Exception as e:
                print(f"    Error adding message {i+1}: {e}")
                continue
        
        print(f"  Completed loading conversation: {title}")
    
    async def load_all_conversations(self, json_file_path: str, group_id: str = "conversations",
                                   batch_size: int = 10, start_from: int = 0) -> None:
        """Load all conversations from the JSON file into Graphiti.

        Args:
            json_file_path: Path to the conversations JSON file
            group_id: Group ID for all conversation episodes
            batch_size: Number of conversations to process in each batch
            start_from: Index to start processing from (for resuming)
        """
        print("Starting conversation loading process...")

        # Initialize Graphiti indices and constraints
        await self.graphiti.build_indices_and_constraints()
        print("Graphiti indices and constraints built")

        # Parse conversations
        conversations = self.parse_conversations(json_file_path)

        # Apply start_from filter
        if start_from > 0:
            conversations = conversations[start_from:]
            print(f"Starting from conversation {start_from + 1}")

        # Process conversations in batches
        total_conversations = len(conversations)
        processed = 0

        for i in range(0, total_conversations, batch_size):
            batch = conversations[i:i + batch_size]
            batch_start = start_from + i + 1
            batch_end = min(start_from + i + len(batch), start_from + total_conversations)

            print(f"\n📦 Processing batch: conversations {batch_start}-{batch_end}")

            for j, conversation in enumerate(batch):
                conv_num = batch_start + j
                print(f"\nProcessing conversation {conv_num}/{start_from + total_conversations}")
                try:
                    await self.load_conversation(conversation, group_id)
                    processed += 1
                except Exception as e:
                    title = conversation.get('title', 'Unknown')
                    print(f"Error loading conversation '{title}': {e}")
                    continue

            print(f"✅ Completed batch. Processed {processed} conversations so far.")

            # Optional: Add a small delay between batches to prevent overwhelming the system
            if i + batch_size < total_conversations:
                print("⏳ Brief pause before next batch...")
                await asyncio.sleep(1)

        print(f"\n🎉 Completed loading {processed} conversations into Graphiti!")
    
    async def close(self):
        """Close the Graphiti connection."""
        if hasattr(self.graphiti, 'driver') and self.graphiti.driver:
            await self.graphiti.driver.close()


async def main():
    """Main function to run the conversation loader."""
    # Configuration - update these with your settings
    NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    NEO4J_USER = os.getenv('NEO4J_USER', 'neo4j')
    NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD', 'password')

    # Path to your conversations JSON file
    CONVERSATIONS_FILE = os.getenv('CONVERSATIONS_FILE', 'conversations copy.json')

    # Group ID for organizing the conversations
    GROUP_ID = os.getenv('GROUP_ID', 'chatgpt_conversations')

    # Batch processing settings
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', '1'))   # Process 1 conversation at a time for testing
    START_FROM = int(os.getenv('START_FROM', '0'))   # Start from beginning (0-indexed)

    # Verify the file exists
    if not os.path.exists(CONVERSATIONS_FILE):
        print(f"Error: File '{CONVERSATIONS_FILE}' not found!")
        print("Please make sure the file path is correct.")
        return

    print(f"Configuration:")
    print(f"  📁 File: {CONVERSATIONS_FILE}")
    print(f"  🏷️  Group ID: {GROUP_ID}")
    print(f"  📦 Batch size: {BATCH_SIZE}")
    print(f"  ▶️  Starting from: {START_FROM}")
    print(f"  🔗 Neo4j URI: {NEO4J_URI}")
    print()

    # Create and run the loader
    loader = ConversationLoader(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)

    try:
        await loader.load_all_conversations(CONVERSATIONS_FILE, GROUP_ID, BATCH_SIZE, START_FROM)
    except Exception as e:
        print(f"Error during loading: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await loader.close()


if __name__ == "__main__":
    asyncio.run(main())
