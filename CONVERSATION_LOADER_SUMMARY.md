# ChatGPT Conversation Loader for Graphiti - Complete Setup

## 📁 Files Created

I've created a complete system to load your ChatGPT conversations into Graphiti:

### Core Files
- **`conversation_loader.py`** - Main script to load conversations into Graphiti
- **`test_parser.py`** - Test script to verify JSON parsing (run this first!)
- **`requirements_loader.txt`** - Python dependencies
- **`setup_conversation_loader.sh`** - Automated setup script

### Documentation
- **`README_conversation_loader.md`** - Detailed documentation
- **`CONVERSATION_LOADER_SUMMARY.md`** - This summary file
- **`.env.example`** - Environment variables template (already exists)

## 🚀 Quick Start

### 1. Run the Setup Script
```bash
./setup_conversation_loader.sh
```

### 2. Configure Environment
Edit the `.env` file with your credentials:
```bash
nano .env
```

Required variables:
```
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
OPENAI_API_KEY=your_openai_key
```

### 3. Test the Parser
```bash
python3 test_parser.py
```

### 4. Load Your Conversations
```bash
python3 conversation_loader.py
```

## 📊 Your Data Summary

From your `conversations copy.json` file:
- **751 conversations** total
- **10,856 messages** across all conversations
- Date range: 2022-2024
- Topics include: ADHD, programming, taxes, AI, and much more

## ⚙️ Advanced Configuration

You can customize the loading process with environment variables:

```bash
# Process conversations in smaller batches
export BATCH_SIZE=5

# Resume from a specific conversation (0-indexed)
export START_FROM=100

# Use a different file
export CONVERSATIONS_FILE="my_conversations.json"

# Use a different group ID
export GROUP_ID="my_chatgpt_data"
```

## 🔧 Features

### Batch Processing
- Processes conversations in configurable batches (default: 10)
- Can resume from any point if interrupted
- Progress tracking and error handling

### Data Structure
Each message becomes a Graphiti episode with:
- **Name**: "Conversation Title - Message N"
- **Content**: "Role: Message content"
- **Source**: Message type
- **Timestamp**: Original message time
- **Group ID**: For organization

### Error Handling
- Skips problematic messages without stopping
- Detailed error reporting
- Graceful handling of different message types

## 🎯 What Happens Next

Once loaded into Graphiti, you can:

1. **Search conversations**: Find specific topics or discussions
2. **Extract entities**: Graphiti will identify people, places, concepts
3. **Build relationships**: Connect related conversations and topics
4. **Query knowledge**: Ask questions about your conversation history
5. **Analyze patterns**: Understand your interests and discussion themes

## 🔍 Example Queries (after loading)

With your conversations in Graphiti, you could ask:
- "What did I learn about ADHD medications?"
- "Show me all conversations about programming"
- "What tax advice did I receive?"
- "Find discussions about work-life balance"

## 📈 Performance Notes

- **Processing time**: ~10-15 seconds per conversation
- **Total estimated time**: ~2-3 hours for all 751 conversations
- **Memory usage**: Moderate (processes in batches)
- **Database size**: Expect significant growth in Neo4j

## 🛠️ Troubleshooting

### Common Issues
1. **Neo4j connection**: Ensure Neo4j is running
2. **OpenAI API**: Check your API key and credits
3. **Memory issues**: Reduce BATCH_SIZE
4. **File not found**: Check CONVERSATIONS_FILE path

### Resume Processing
If interrupted, resume with:
```bash
export START_FROM=200  # Start from conversation 201
python3 conversation_loader.py
```

## 📚 Next Steps

1. **Run the test parser** to verify your data
2. **Set up your environment** with credentials
3. **Start with a small batch** to test the system
4. **Monitor the process** and adjust batch size if needed
5. **Explore your data** in Graphiti once loaded

## 🎉 Benefits

After loading, you'll have:
- **Searchable conversation history**
- **Entity extraction** (people, topics, concepts)
- **Relationship mapping** between conversations
- **Temporal organization** of your discussions
- **AI-powered insights** into your conversation patterns

Your 751 conversations will become a rich, queryable knowledge graph that you can explore and analyze in ways that weren't possible with the raw JSON data!
